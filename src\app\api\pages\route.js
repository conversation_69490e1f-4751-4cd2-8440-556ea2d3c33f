import { NextResponse } from 'next/server';
import { connectDB } from '@/lib/mongodb';
import { Page } from '@/models/Page';

// GET - Fetch all pages with search, filtering, and pagination
export async function GET(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page')) || 1;
    const limit = parseInt(searchParams.get('limit')) || 10;
    const search = searchParams.get('search') || '';
    const section = searchParams.get('section') || '';
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Build query
    const query = {};
    
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { body: { $regex: search, $options: 'i' } },
        { section: { $regex: search, $options: 'i' } }
      ];
    }
    
    if (section) {
      query.section = section;
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query with pagination
    const skip = (page - 1) * limit;
    const [pages, total] = await Promise.all([
      Page.find(query)
        .sort(sort)
        .skip(skip)
        .limit(limit)
        .lean(),
      Page.countDocuments(query)
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: pages,
      pagination: {
        currentPage: page,
        totalPages,
        totalItems: total,
        itemsPerPage: limit,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching pages:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to fetch pages', error: error.message },
      { status: 500 }
    );
  }
}

// POST - Create or update pages (handles both single and bulk operations)
export async function POST(request) {
  try {
    await connectDB();
    
    const body = await request.json();
    
    // Handle single page creation/update
    if (body.section) {
      const existingPage = await Page.findOne({ section: body.section });
      
      if (existingPage) {
        // Update existing page
        Object.assign(existingPage, body);
        const updatedPage = await existingPage.save();
        
        return NextResponse.json({
          success: true,
          message: 'Page updated successfully',
          data: updatedPage
        });
      } else {
        // Create new page
        const newPage = new Page(body);
        const savedPage = await newPage.save();
        
        return NextResponse.json({
          success: true,
          message: 'Page created successfully',
          data: savedPage
        }, { status: 201 });
      }
    }
    
    // Handle bulk operations
    if (Array.isArray(body)) {
      const results = [];
      
      for (const pageData of body) {
        try {
          const existingPage = await Page.findOne({ section: pageData.section });
          
          if (existingPage) {
            Object.assign(existingPage, pageData);
            const updatedPage = await existingPage.save();
            results.push({ success: true, data: updatedPage });
          } else {
            const newPage = new Page(pageData);
            const savedPage = await newPage.save();
            results.push({ success: true, data: savedPage });
          }
        } catch (error) {
          results.push({ success: false, error: error.message, section: pageData.section });
        }
      }
      
      return NextResponse.json({
        success: true,
        message: 'Bulk operation completed',
        results
      });
    }

    return NextResponse.json(
      { success: false, message: 'Invalid request format' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error creating/updating pages:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to create/update pages', error: error.message },
      { status: 500 }
    );
  }
}

// PATCH - Bulk update pages with actions
export async function PATCH(request) {
  try {
    await connectDB();
    
    const { action, ids, data } = await request.json();
    
    if (!action || !ids || !Array.isArray(ids)) {
      return NextResponse.json(
        { success: false, message: 'Action and IDs array are required' },
        { status: 400 }
      );
    }

    let result;
    
    switch (action) {
      case 'bulkUpdate':
        if (!data) {
          return NextResponse.json(
            { success: false, message: 'Data is required for bulk update' },
            { status: 400 }
          );
        }
        
        result = await Page.updateMany(
          { _id: { $in: ids } },
          { $set: data }
        );
        
        return NextResponse.json({
          success: true,
          message: `Updated ${result.modifiedCount} pages`,
          modifiedCount: result.modifiedCount
        });
        
      default:
        return NextResponse.json(
          { success: false, message: 'Invalid action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in bulk update:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to perform bulk update', error: error.message },
      { status: 500 }
    );
  }
}

// DELETE - Bulk delete pages
export async function DELETE(request) {
  try {
    await connectDB();
    
    const { searchParams } = new URL(request.url);
    const idsParam = searchParams.get('ids');
    
    if (!idsParam) {
      return NextResponse.json(
        { success: false, message: 'IDs parameter is required' },
        { status: 400 }
      );
    }

    const ids = idsParam.split(',').filter(id => id.trim());
    
    if (ids.length === 0) {
      return NextResponse.json(
        { success: false, message: 'At least one valid ID is required' },
        { status: 400 }
      );
    }

    const result = await Page.deleteMany({ _id: { $in: ids } });

    return NextResponse.json({
      success: true,
      message: `Deleted ${result.deletedCount} pages`,
      deletedCount: result.deletedCount
    });

  } catch (error) {
    console.error('Error deleting pages:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to delete pages', error: error.message },
      { status: 500 }
    );
  }
}
