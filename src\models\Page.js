import mongoose from 'mongoose';
const { Schema } = mongoose;

// Secondary entry schema for 'the island' and 'experiences' sections
const SecondaryEntrySchema = new Schema({
  image: {
    type: String,
    required: true,
    validate: {
      validator: function(v) {
        return v.includes('firebasestorage.googleapis.com') || v.startsWith('/uploads/');
      },
      message: 'Image must be a valid Firebase Storage URL or local path'
    }
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body: {
    type: String,
    required: true,
    trim: true,
    maxlength: [5000, 'Body cannot exceed 5000 characters']
  }
}, { _id: true });

// Testimonial schema for 'testimonials' section
const TestimonialSchema = new Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: [100, 'Name cannot exceed 100 characters']
  },
  comment: {
    type: String,
    required: true,
    trim: true,
    maxlength: [1000, 'Comment cannot exceed 1000 characters']
  }
}, { _id: true });

// Main Page schema
const PageSchema = new Schema({
  section: {
    type: String,
    required: true,
    enum: ['the island', 'experiences', 'testimonials', 'location & contacts'],
    unique: true
  },
  
  // Fields for 'the island' and 'experiences' sections
  image: {
    type: String,
    required: function() {
      return ['the island', 'experiences', 'location & contacts'].includes(this.section);
    },
    validate: {
      validator: function(v) {
        if (!v && ['the island', 'experiences', 'location & contacts'].includes(this.section)) {
          return false;
        }
        if (v) {
          return v.includes('firebasestorage.googleapis.com') || v.startsWith('/uploads/');
        }
        return true;
      },
      message: 'Image must be a valid Firebase Storage URL or local path'
    }
  },
  title: {
    type: String,
    required: function() {
      return ['the island', 'experiences', 'location & contacts'].includes(this.section);
    },
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  body: {
    type: String,
    required: function() {
      return ['the island', 'experiences', 'location & contacts'].includes(this.section);
    },
    trim: true,
    maxlength: [5000, 'Body cannot exceed 5000 characters']
  },
  
  // Secondary entries for 'the island' and 'experiences' sections
  secondaryEntries: {
    type: [SecondaryEntrySchema],
    default: [],
    validate: {
      validator: function(v) {
        return v.length <= 10; // Maximum 10 secondary entries
      },
      message: 'Cannot have more than 10 secondary entries'
    }
  },
  
  // Testimonials array for 'testimonials' section
  testimonials: {
    type: [TestimonialSchema],
    default: [],
    validate: [
      {
        validator: function(v) {
          return v.length <= 20; // Maximum 20 testimonials
        },
        message: 'Cannot have more than 20 testimonials'
      },
      {
        validator: function(v) {
          // Check for unique names within testimonials
          const names = v.map(t => t.name.toLowerCase());
          return names.length === new Set(names).size;
        },
        message: 'Testimonial names must be unique'
      }
    ]
  },
  
  // URL field for 'location & contacts' section
  url: {
    type: String,
    required: function() {
      return this.section === 'location & contacts';
    },
    trim: true,
    validate: {
      validator: function(v) {
        if (!v && this.section === 'location & contacts') {
          return false;
        }
        if (v) {
          try {
            new URL(v);
            return true;
          } catch {
            return false;
          }
        }
        return true;
      },
      message: 'URL must be a valid URL format'
    }
  }
}, { 
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better query performance
PageSchema.index({ section: 1 });
PageSchema.index({ createdAt: -1 });

// Virtual for display formatting
PageSchema.virtual('displaySection').get(function() {
  return this.section.split(' ').map(word => 
    word.charAt(0).toUpperCase() + word.slice(1)
  ).join(' ');
});

// Static method to initialize default pages
PageSchema.statics.initializeDefaultPages = async function() {
  const sections = ['the island', 'experiences', 'testimonials', 'location & contacts'];
  
  for (const section of sections) {
    const existingPage = await this.findOne({ section });
    if (!existingPage) {
      const defaultData = {
        section,
        ...(section === 'testimonials' ? {
          testimonials: []
        } : section === 'location & contacts' ? {
          title: 'Contact Information',
          body: 'Get in touch with us for more information about Elephant Island Lodge.',
          url: 'https://example.com',
          image: ''
        } : {
          title: `Welcome to ${section.charAt(0).toUpperCase() + section.slice(1)}`,
          body: `Discover the beauty and wonder of ${section}.`,
          image: '',
          secondaryEntries: []
        })
      };
      
      await this.create(defaultData);
    }
  }
};

// Pre-save middleware for data validation and cleanup
PageSchema.pre('save', function(next) {
  // Clean up empty secondary entries
  if (this.secondaryEntries) {
    this.secondaryEntries = this.secondaryEntries.filter(entry => 
      entry.title && entry.body && entry.image
    );
  }
  
  // Clean up empty testimonials
  if (this.testimonials) {
    this.testimonials = this.testimonials.filter(testimonial => 
      testimonial.name && testimonial.comment
    );
  }
  
  next();
});

// Use existing model if it exists, otherwise create a new one
export const Page = mongoose.models.Page || mongoose.model('Page', PageSchema);
