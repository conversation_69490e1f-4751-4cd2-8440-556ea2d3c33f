import { NextResponse } from 'next/server';
import { createFileUploadHandler } from '@/lib/server-file-upload';

// Configure upload settings for pages
const UPLOAD_CONFIG = {
  folder: 'pages',
  maxFileSize: 15 * 1024 * 1024, // 15MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'],
  maxFiles: 10 // Allow multiple files for bulk upload
};

// POST - Handle file uploads for pages
export async function POST(request) {
  try {
    console.log('Pages upload API called');
    
    // Create upload handler with pages-specific configuration
    const uploadHandler = createFileUploadHandler(UPLOAD_CONFIG);
    
    // Process the upload
    const result = await uploadHandler(request);
    
    console.log('Pages upload result:', {
      success: result.success,
      fileCount: result.files ? result.files.length : 0,
      storage: result.files?.[0]?.storage
    });
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.files.length === 1 
          ? 'Page image uploaded successfully' 
          : `${result.files.length} page images uploaded successfully`,
        files: result.files,
        data: result.files // For compatibility with existing components
      });
    } else {
      return NextResponse.json(
        { 
          success: false, 
          message: result.message || 'Upload failed',
          error: result.error 
        },
        { status: 400 }
      );
    }

  } catch (error) {
    console.error('Pages upload error:', error);
    
    return NextResponse.json(
      { 
        success: false, 
        message: 'Failed to upload page images',
        error: error.message 
      },
      { status: 500 }
    );
  }
}

// GET - Get upload configuration (for frontend validation)
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      config: {
        maxFileSize: UPLOAD_CONFIG.maxFileSize,
        allowedTypes: UPLOAD_CONFIG.allowedTypes,
        maxFiles: UPLOAD_CONFIG.maxFiles,
        folder: UPLOAD_CONFIG.folder
      }
    });
  } catch (error) {
    console.error('Error getting upload config:', error);
    return NextResponse.json(
      { success: false, message: 'Failed to get upload configuration' },
      { status: 500 }
    );
  }
}
